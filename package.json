{"name": "writer-studio", "productName": "WriterStudio", "version": "0.8.3", "description": "Best application for writing books", "main": ".vite/build/main.js", "config": {"forge": "./forge.config.mjs"}, "scripts": {"package": "electron-forge package", "make": "electron-forge make", "publish": "electron-forge publish", "lint": "eslint .", "clean": "rimraf dist .vite out", "forge-start": "electron-forge start", "start": "npm run forge-start", "update-deps": "npm update", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "keywords": [], "author": {"name": "ralist", "email": "<EMAIL>"}, "devDependencies": {"@electron-forge/cli": "^7.8.0", "@electron-forge/maker-deb": "^7.8.0", "@electron-forge/maker-rpm": "^7.8.0", "@electron-forge/maker-squirrel": "^7.8.0", "@electron-forge/maker-zip": "^7.8.0", "@electron-forge/plugin-auto-unpack-natives": "^7.8.0", "@electron-forge/plugin-fuses": "^7.8.0", "@electron-forge/plugin-vite": "^7.8.0", "@electron/fuses": "^1.8.0", "@eslint-react/eslint-plugin": "^1.48.3", "@eslint/eslintrc": "^3.0.0", "@eslint/js": "^9.0.0", "@jest/globals": "^30.0.0", "@types/better-sqlite3": "^7.6.12", "@types/electron-squirrel-startup": "^1.0.2", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/lodash-es": "^4.17.12", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "electron": "^35.1.5", "eslint-import-resolver-typescript": "^4.2.5", "jest": "^30.0.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.2.6"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@lexical/react": "^0.30.0", "@lexical/utils": "^0.30.0", "better-sqlite3": "^11.9.1", "classnames": "^2.5.1", "electron-squirrel-startup": "^1.0.1", "electron-store": "^10.0.1", "glob": "^11.0.1", "lexical": "^0.30.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "lucide-react": "^0.488.0", "mitt": "^3.0.1", "openai": "^4.91.1", "react": "^19.0.0", "react-dom": "^19.0.0", "zustand": "^5.0.3"}}